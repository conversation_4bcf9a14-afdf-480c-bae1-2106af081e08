<transition :name="transitionDirection" mode="out-in">
  <div>
    <div class="main-right box-border space-x-2 pr-4 pt-2">
      <!--选择订单内容-->
      <div
        class="o-1vh bg-white relative flex flex-1 flex-col overflow-hidden rounded-lg"
      >
        <el-tabs
          v-model="billingType"
          @tab-click="billingChangeType"
          class="shrink-0 px-4 pt-4"
        >
          <el-tab-pane label="服务" name="0"></el-tab-pane>
          <el-tab-pane label="产品" name="1"></el-tab-pane>
          <el-tab-pane label="扣卡" name="2"></el-tab-pane>
        </el-tabs>
        <div
          class="z-1 absolute right-4 top-7 flex space-x-1 pl-2"
          v-if="billingType == 2"
        >
          <span class="text-sm">隐藏失效卡</span>
          <el-switch v-model="isShowLoseEfficacyCard"> </el-switch>
        </div>
        <div class="o-title-box-shadow z-1 relative shrink-0 px-4 pb-4">
          <div class="el-input el-input--prefix">
            <input
              type="text"
              autocomplete="off"
              placeholder="请输入服务名称"
              name="search_keyword"
              class="el-input__inner"
              v-model.trim="search_keyword"
              ref="search_keyword"
            />
            <span class="el-input__prefix">
              <i class="el-input__icon el-icon-search"></i>
            </span>
          </div>
        </div>
        <div
          class="o-scrollbar h-0 flex-1 overflow-y-auto p-4"
          ref="cardWrap"
          v-loading="loading"
        >
          <div
            class="gap-2"
            :class="billingType == 2 ?'o-little-server-card-grid-box-long':'o-little-server-card-grid-box'"
          >
            <template v-if="billingType == 0">
              <template v-for="(item,index) in cashier_open_order_service_name">
                <div
                  class="bg-gray-50 border-gray-200 hover:shadow-md hover:translate-y--1 active:scale-98 transform cursor-pointer rounded-md border border-solid px-4 py-2 transition-all hover:border-primary"
                  v-if="search_keyword === '' ? true : item.service_name.includes(search_keyword)"
                  :key="'service'+index"
                  @click="bind_add_server(item, index)"
                  :class="{ 'o-service-card-pulse': item._justAdded }"
                >
                  <div class="flex h-12 items-center">
                    <div
                      class="line-clamp-2 text-lg font-bold leading-5"
                      v-html="highlightSearchKeyword(item.service_name)"
                    ></div>
                  </div>
                  <div><span class="text-xs">￥</span>{{item.price}}</div>
                </div>
              </template>
            </template>
            <template v-if="billingType == 1">
              <template v-for="(item,index) in cashier_open_order_product_name">
                <div
                  v-if="search_keyword === '' ? true : item.product_name.includes(search_keyword)"
                  class="bg-gray-50 border-gray-200 hover:shadow-md hover:translate-y--1 active:scale-98 transform cursor-pointer rounded-md border border-solid px-4 py-2 transition-all hover:border-primary"
                  :key="'product'+index"
                  @click="bind_add_product(item, index)"
                  :class="{ 'o-service-card-pulse': item._justAdded }"
                >
                  <div class="flex h-12 items-center">
                    <div
                      class="line-clamp-2 text-lg font-bold leading-5"
                      v-html="highlightSearchKeyword(item.product_name)"
                    ></div>
                  </div>
                  <div><span class="text-xs">￥</span>{{item.realPrice}}</div>
                </div>
              </template>
            </template>
            <template v-if="billingType == 2">
              <template v-for="(item,index) in sampleMemberCardInfo">
                <div
                  v-if="search_keyword === '' ? true : item.service_name.includes(search_keyword)"
                  id="searchCardBox"
                  class="bg-gray-50 border-gray-200 hover:shadow-md hover:translate-y--1 active:scale-98 transform cursor-pointer rounded-md border border-solid px-4 py-2 transition-all hover:border-primary"
                  :key="'card'+index"
                  @click="bind_add_membercard(item, item.cardInfo)"
                  :class="{ 'o-service-card-pulse': item._justAdded }"
                >
                  <div
                    class="items-cneter flex h-12 items-center justify-between"
                  >
                    <div class="flex items-center space-x-1">
                      <div
                        class="line-clamp-2 text-lg font-bold leading-5"
                        v-html="highlightSearchKeyword(item.service_name)"
                      ></div>
                      <el-tag v-if="item.givenum!=0" type="danger" size="mini">
                        {{item.cardSource==-3?'奖':'赠'}}
                      </el-tag>
                    </div>
                    <div
                      v-if="item.statusName!='正常'"
                      class="text-red-500 shrink-0"
                    >
                      {{item.statusName}}
                    </div>
                  </div>
                  <div class="mt-1 flex items-baseline justify-between text-sm">
                    <div class="">￥{{item.price}}</div>
                    <div>
                      <span
                        class=""
                        v-if="item.equityTypes==3 && item.infinite==1"
                      >
                        无限次卡
                      </span>
                      <span v-if="item.equityTypes==3 && item.infinite==2">
                        <span class="">剩</span>
                        <span class="text-base font-bold">
                          {{item.num-cardUseNum2(item,item.cardInfo)}}
                        </span>
                        <span class="">次</span>
                      </span>
                      <span class="" v-if="item.equityTypes==2">
                        {{item.discount}}折
                      </span>
                    </div>
                  </div>
                  <div class="flex justify-between space-x-4 text-sm">
                    <div>{{item.cardInfo.card_name}}</div>

                    <div v-if="item.indateName=='永久有效'" class="shrink-0">
                      {{item.indateName}}
                    </div>
                    <div v-else class="shrink-0">{{item.indateName}}</div>
                  </div>
                </div>
              </template>
              <template v-for="(item,index) in sampleMemberCardInfo_runOut">
                <div
                  v-if="search_keyword === '' ? true : item.service_name.includes(search_keyword)"
                  class="bg-gray-50 border-gray-200 text-gray-500 transform rounded-md border border-solid px-4 py-2 transition-all"
                  :key="'card_runOut'+index"
                >
                  <div
                    class="items-cneter flex h-12 items-center justify-between"
                  >
                    <div class="flex items-center space-x-1">
                      <div
                        class="line-clamp-2 text-lg font-bold leading-5"
                        v-html="highlightSearchKeyword(item.service_name)"
                      ></div>
                      <el-tag v-if="item.givenum!=0" type="danger" size="mini">
                        {{item.cardSource==-3?'奖':'赠'}}
                      </el-tag>
                    </div>
                    <div
                      v-if="item.statusName!='正常'"
                      class="text-red-500 shrink-0"
                    >
                      {{item.statusName}}
                    </div>
                  </div>
                  <div class="mt-1 flex items-baseline justify-between text-sm">
                    <div class="text-gray-500">￥{{item.price}}</div>
                    <div>
                      <span
                        class="text-gray-500"
                        v-if="item.equityTypes==3 && item.infinite==1"
                      >
                        无限次卡
                      </span>
                      <span v-if="item.equityTypes==3 && item.infinite==2">
                        <span class="text-gray-500">剩</span>
                        <span class="text-base font-bold">
                          {{item.num-cardUseNum2(item,item.cardInfo)}}
                        </span>
                        <span class="text-gray-500">次</span>
                      </span>
                      <span class="text-gray-500" v-if="item.equityTypes==2">
                        {{item.discount}}折
                      </span>
                    </div>
                  </div>
                  <div
                    class="text-gray-500 flex justify-between space-x-4 text-sm"
                  >
                    <div>{{item.cardInfo.card_name}}</div>

                    <div v-if="item.indateName=='永久有效'" class="shrink-0">
                      {{item.indateName}}
                    </div>
                    <div v-else class="shrink-0">{{item.indateName}}</div>
                  </div>
                </div>
              </template>
            </template>
          </div>
        </div>
      </div>
      <!--开单内容-->
      <div
        class="o-1vh bg-white flex shrink-0 flex-col overflow-hidden rounded-lg"
        style="width: 650px"
      >
        <member-search-bar
          ref="memberSearchBar"
          :login-info="loginInfo"
          :show-member-search.sync="isShowMemberSearch"
          @handle-select-member="handleMemberSelect"
          @handle-clear="clearPage"
        />
        <div
          class="o-scrollbar h-0 flex-1 overflow-y-auto"
          ref="rightScrollContainer"
        >
          <div class="p-4">
            <transition-group
              name="o-service-card-list"
              tag="div"
              class="space-y-3"
              ref="serviceCardsList"
            >
              <template v-if="billingType == 0 || billingType == 1">
                <div
                  class="o-service-card bg-white border-gray-200 relative overflow-hidden rounded-md border border-solid transition-all hover:shadow-lg"
                  :class="{ 'o-service-card-item-removing': item._removing }"
                  v-for="(item,index) in C_open_order_specifications_save"
                  :key="item.id + '_' + (item._animationId || index)"
                >
                  <div
                    class="o-service-card-title-bg border-b-solid border-b-1 border-b-gray-200 px-6 pb-3 pt-3 transition-all"
                  >
                    <div class="flex items-center">
                      <div class="pr-4 text-xl font-bold">
                        {{item.service_name }}
                      </div>
                      <div
                        class="o-tag text-xs"
                        :class="getOrderTypeColor(item.zhonglei)"
                      >
                        {{item.zhonglei == 1 ? '服务' : '产品'}}
                      </div>
                      <div
                        class="flex flex-1 items-center justify-end pl-6 pr-4"
                      >
                        <div
                          class="el-icon-delete hover:text-red cursor-pointer transition"
                          @click="zhk_open_details_price_del(index)"
                        ></div>
                      </div>
                    </div>
                    <div class="mt-3 flex items-center justify-between">
                      <div class="flex shrink-0 items-center space-x-6">
                        <div
                          class="o-price-select-tag"
                          @click="handleSetUnitPrice(index, item.price)"
                          @mouseenter="handlePriceTagHover($event, true)"
                          @mouseleave="handlePriceTagHover($event, false)"
                        >
                          <div>原价</div>
                          <div>￥{{item.price}}</div>
                        </div>
                        <div
                          class="o-price-select-tag"
                          @click="handleSetUnitPrice(index, item.member_price / 100)"
                          @mouseenter="handlePriceTagHover($event, true)"
                          @mouseleave="handlePriceTagHover($event, false)"
                          v-if="item.member_price != item.zhk_price && item.member_price>0"
                        >
                          <div>会员价</div>
                          <div>￥{{item.member_price | filterMoney}}</div>
                        </div>
                      </div>
                      <div class="flex flex-1 justify-end">
                        <div
                          v-if="!item.technician_name && !item.saler_name"
                          class="w-fit cursor-pointer text-primary hover:text-primary/80"
                          @click="handleMergeSelect(index)"
                        >
                          设置提成
                        </div>
                        <div class="w-fit">
                          <div
                            v-if="item.technician_name"
                            class="mt-2 flex w-fit"
                          >
                            <div class="text-gray shrink-0" style="width: 70px">
                              <technician-name></technician-name>
                              ：
                            </div>
                            <div
                              class="cursor-pointer text-primary hover:text-primary/80"
                              @click="handleMergeSelect(index)"
                            >
                              {{item.technician_name}}
                            </div>
                          </div>
                          <div v-if="item.saler_name" class="mt-2 flex w-fit">
                            <div class="text-gray shrink-0" style="width: 70px">
                              销售：
                            </div>
                            <div
                              class="cursor-pointer text-primary hover:text-primary/80"
                              @click="handleMergeSelect(index)"
                            >
                              {{item.saler_name}}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="flex items-center justify-between px-5 py-4 text-sm"
                  >
                    <div class="flex items-center">
                      <div class="text-gray-500 shrink-0 pr-2">单价</div>
                      <div
                        class="el-input el-input--small el-input--prefix"
                        style="width: 152px"
                      >
                        <input
                          type="text"
                          class="o-price-select-hover el-input__inner no-spinners"
                          style="
                            font-size: 18px;
                            font-weight: bolder;
                            text-align: right;
                          "
                          v-model.trim="item.unitPrice"
                          @input="handleUnitPriceChange(index)"
                          onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 46"
                          oninput="
                            // 只允许数字和小数点
                            this.value = this.value.replace(/[^0-9.]/g, '');
                            // 防止多个小数点
                            if(this.value.split('.').length > 2) {
                              this.value = this.value.substring(0, this.value.lastIndexOf('.'));
                            }
                            // 限制最大值
                            if(parseFloat(this.value) >= 10000000) this.value = '9999999.99';
                            // 限制小数位数
                            if(this.value.includes('.') && this.value.split('.')[1] && this.value.split('.')[1].length > 2) {
                              let parts = this.value.split('.');
                              this.value = parts[0] + '.' + parts[1].substring(0, 2);
                            }
                          "
                        />
                        <span class="el-input__prefix center">
                          <span class="el-input__prefix-inner pl-1">￥</span>
                        </span>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <div class="text-gray-500 shrink-0 pr-2">数量</div>
                      <div class="o-numberInput-box" style="width: 130px">
                        <i class="el-icon-minus" @click="jianshao(index)"></i>
                        <input
                          class="el-input__inner text-center"
                          v-model="item.num"
                          size="small"
                          style="
                            width: 80px;
                            font-size: 18px;
                            font-weight: bolder;
                          "
                          step="1"
                          maxlength="4"
                          @input="handleNumInputChange($event, index)"
                        />
                        <i class="el-icon-plus" @click="zengjia(index)"></i>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <div class="text-gray-500 shrink-0 pr-2">小计</div>
                      <div
                        class="el-input el-input--small el-input--prefix"
                        style="width: 152px"
                      >
                        <input
                          type="text"
                          class="el-input__inner no-spinners"
                          style="
                            font-size: 18px;
                            font-weight: bolder;
                            text-align: right;
                          "
                          v-model.trim="item.subtotal"
                          @input="handleSubtotalChange(index)"
                          onkeypress="return (event.charCode >= 48 && event.charCode <= 57) || event.charCode == 46"
                          oninput="
                            // 只允许数字和小数点
                            this.value = this.value.replace(/[^0-9.]/g, '');
                            // 防止多个小数点
                            if(this.value.split('.').length > 2) {
                              this.value = this.value.substring(0, this.value.lastIndexOf('.'));
                            }
                            // 限制最大值
                            if(parseFloat(this.value) >= 10000000) this.value = '9999999.99';
                            // 限制小数位数
                            if(this.value.includes('.') && this.value.split('.')[1] && this.value.split('.')[1].length > 2) {
                              let parts = this.value.split('.');
                              this.value = parts[0] + '.' + parts[1].substring(0, 2);
                            }
                          "
                        />
                        <span class="el-input__prefix center">
                          <span class="el-input__prefix-inner pl-1">￥</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
              <template v-if="billingType == 2">
                <div
                  id="selectServiceCardBox"
                  class="o-service-card bg-white border-gray-200 relative overflow-hidden rounded-md border border-solid transition-all hover:shadow-lg"
                  :class="{ 'o-service-card-item-removing': item._removing }"
                  v-for="(item,index) in C_open_order_specifications_save"
                  :key="item.id + '_' + (item._animationId || index)"
                >
                  <div
                    class="o-service-card-title-bg border-b-solid border-b-1 border-b-gray-200 px-6 pb-3 pt-3 transition-all"
                  >
                    <div class="flex items-center">
                      <div class="pr-4 text-xl font-bold">
                        {{item.service_name}}
                      </div>
                      <div
                        class="o-tag text-xs"
                        :class="getOrderTypeColor(item.zhonglei)"
                      >
                        服务
                      </div>
                      <div
                        class="flex flex-1 items-center justify-end pl-6 pr-4"
                      >
                        <div
                          class="el-icon-delete hover:text-red cursor-pointer transition"
                          @click="zhk_open_details_price_del(index)"
                        ></div>
                      </div>
                    </div>
                    <div class="mt-3 flex items-center justify-between">
                      <div class="flex shrink-0 items-center space-x-6">
                        <div
                          class="o-price-select-tag disable"
                          @mouseenter="handlePriceTagHover($event, true)"
                          @mouseleave="handlePriceTagHover($event, false)"
                        >
                          <div>单价</div>
                          <div>￥{{item.price}}</div>
                        </div>
                      </div>
                      <div class="flex flex-1 justify-end">
                        <div
                          v-if="!item.technician_name && !item.saler_name"
                          class="w-fit cursor-pointer text-primary hover:text-primary/80"
                          @click="handleMergeSelect(index)"
                        >
                          设置提成
                        </div>
                        <div class="w-fit">
                          <div
                            v-if="item.technician_name"
                            class="mt-2 flex w-fit"
                          >
                            <div class="text-gray shrink-0" style="width: 70px">
                              <technician-name></technician-name>
                              ：
                            </div>
                            <div
                              class="cursor-pointer text-primary hover:text-primary/80"
                              @click="handleMergeSelect(index)"
                            >
                              {{item.technician_name}}
                            </div>
                          </div>
                          <div v-if="item.saler_name" class="mt-2 flex w-fit">
                            <div class="text-gray shrink-0" style="width: 70px">
                              销售：
                            </div>
                            <div
                              class="cursor-pointer text-primary hover:text-primary/80"
                              @click="handleMergeSelect(index)"
                            >
                              {{item.saler_name}}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  <div
                    class="flex items-center justify-between px-5 py-4 text-sm"
                  >
                    <div class="flex items-center">
                      <div class="text-gray-500 shrink-0 pr-2">单价</div>
                      <div
                        class="el-input el-input--small el-input--prefix"
                        style="width: 152px"
                      >
                        <input
                          type="text"
                          class="o-price-select-hover el-input__inner no-spinners"
                          disabled
                          style="
                            font-size: 18px;
                            font-weight: bolder;
                            text-align: right;
                          "
                          value="-"
                        />
                        <span class="el-input__prefix center">
                          <span class="el-input__prefix-inner pl-1">￥</span>
                        </span>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <div class="text-gray-500 shrink-0 pr-2">数量</div>
                      <div class="o-numberInput-box" style="width: 130px">
                        <i
                          class="el-icon-minus"
                          @click="handleNumInputMinus(index)"
                        ></i>
                        <span class="w-10 text-center text-lg font-bold">
                          {{item.num}}
                        </span>
                        <i
                          class="el-icon-plus"
                          @click="handleNumInputPlus(index)"
                        ></i>
                      </div>
                    </div>
                    <div class="flex items-center">
                      <div class="text-gray-500 shrink-0 pr-2">小计</div>
                      <div
                        class="el-input el-input--small el-input--prefix"
                        style="width: 152px"
                      >
                        <input
                          type="text"
                          class="el-input__inner no-spinners"
                          disabled
                          style="
                            font-size: 18px;
                            font-weight: bolder;
                            text-align: right;
                          "
                          value="-"
                        />
                        <span class="el-input__prefix center">
                          <span class="el-input__prefix-inner pl-1">￥</span>
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </template>
            </transition-group>
          </div>
        </div>
        <div class="o-total-box relative shrink-0">
          <div class="z-1 relative pt-4">
            <div
              v-if="billingType != 2"
              class="flex h-10 items-center px-6 text-sm"
            >
              <div class="text-gray-500 w-20 shrink-0">选择赠送：</div>
              <div
                class="line-clamp-2 cursor-pointer text-primary"
                @click="chooseGift"
              >
                <span v-if="showGiftData.allPrice==0"> 没有赠送的商品 </span>
                <span v-if="showGiftData.allPrice>0">
                  服务({{showGiftData.serverNum}})/产品({{showGiftData.productNum}})；价值{{showGiftData.allPrice
                  | filterMoney}}元
                </span>
              </div>
            </div>
            <div class="flex h-10 items-center px-6">
              <div class="text-gray-500 w-20 shrink-0 text-sm">内部备注：</div>
              <el-input
                type="text"
                size="small"
                placeholder="请输入"
                v-model="beizhu_info"
              ></el-input>
            </div>
            <div class="o-font-shadow flex items-baseline px-6 py-5 font-bold">
              <span class="text-xl">合计：</span>
              <span class="pr-1 text-lg">￥</span>
              <price-scroll-box :price="pay_all_show"></price-scroll-box>
            </div>
          </div>
        </div>
        <f-base-button
          class="absolute bottom-8 right-10 z-10"
          style="width: 180px"
          title="去结算"
          type="primary"
          @click="goSettle"
        />
      </div>
      <!--选择销售模态框-->
      <el-dialog
        title="选择协助销售"
        :visible.sync="helpStaffVisible"
        width="35%"
        top="7vh"
        :append-to-body="true"
      >
        <div style="height: calc(100vh - 500px); overflow: auto">
          <el-checkbox-group v-model="checkHelpStaffArr">
            <template v-for="(helpStaff,index) in helpStaffAll">
              <div class="xuazne_xiaoshou" v-if="bindStaffId!=helpStaff.id">
                <el-checkbox
                  :label="helpStaff"
                  style="height: 25px; width: 25px"
                >
                  {{helpStaff.nickname}} ({{helpStaff.job_num}})
                </el-checkbox>
              </div>
            </template>
          </el-checkbox-group>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="closeHelpStaffVisible(0)">
            取消
          </el-button>
          <el-button type="primary" @click="closeHelpStaffVisible(1)">
            确定
          </el-button>
        </span>
      </el-dialog>
      <!--合并选择理疗师、销售模态框-->
      <el-dialog
        :visible.sync="isShowMergeDialog"
        width="35%"
        top="7vh"
        :append-to-body="true"
      >
        <div style="max-height: calc(100vh - 300px); overflow: auto">
          <div class="mb-2 text-base font-bold">选择理疗师</div>
          <ul v-for="(item,index) in ji_shis">
            <li class="xuanze_jishi_name">
              <div style="display: flex">
                <div class="xuanze_jishi_name_check">
                  <el-checkbox
                    v-model="item.is_choice_jishi"
                    @change="chioce_ji_shi_name(index,item.is_choice_jishi,item.id)"
                  >
                    {{item.nickname}}
                  </el-checkbox>
                </div>
              </div>
              <div style="display: flex">
                <div class="xuanze_jishi_server_font">点客</div>
                <el-switch
                  v-model="item.is_guest"
                  active-color="#3e63dd"
                  @change="chioce_ji_shi_guest(index,item.is_guest,item.id)"
                  inactive-color="#999999"
                ></el-switch>
              </div>
            </li>
          </ul>
          <p style="height: 50px" v-if="ji_shis.length==0">暂无分配</p>
          <div class="mb-2 mt-8 text-base font-bold">选择销售</div>
          <div class="xuazne_xiaoshou" v-for="(item , index) in xiaoshous ">
            <el-checkbox
              v-model="item.is_choice_xiaoshou"
              style="height: 25px; width: 25px"
              @change="chioce_xiaoshou(index,item.is_choice_xiaoshou,item.id)"
            >
              {{item.nickname}}
            </el-checkbox>
          </div>
        </div>
        <span slot="footer" class="dialog-footer">
          <el-button class="cancel-btn" @click="handleCannelMerge">
            取消
          </el-button>
          <el-button type="primary" @click="handleMergeSubmit">
            确定
          </el-button>
        </span>
      </el-dialog>
      <!-- 点击充值收款后出现开单框 -->
      <template v-if="buy_receipt">
        <app-pay
          :buy-receipt="buy_receipt"
          :login-info="loginInfo"
          :use-card="isRechargeCard"
          :order-no="orderNo"
          :bill-to-pay="billToPay"
          :is-pay-status="isPayStatus"
          :is-consume-cards="billingType==2"
          @close-pay="bindClosePay"
        ></app-pay>
      </template>
      <!-- 收银台选择赠送弹框 -->
      <template v-if="isChooseGift">
        <app-gift
          :login="loginInfo"
          :gift-data-array="billGiftData"
          :member="memberInfo"
          @show-choose-gift="showChooseGift"
          @choose-gift-data="chooseGiftData"
        ></app-gift>
      </template>
    </div>
  </div>
</transition>
